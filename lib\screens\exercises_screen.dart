import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/workout_provider.dart';
import '../models/exercise.dart';
import '../utils/app_theme.dart';
import '../utils/constants.dart';

class ExercisesScreen extends StatefulWidget {
  const ExercisesScreen({super.key});

  @override
  State<ExercisesScreen> createState() => _ExercisesScreenState();
}

class _ExercisesScreenState extends State<ExercisesScreen> {
  final _searchController = TextEditingController();
  String _selectedMuscleGroup = 'All';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<WorkoutProvider>(context, listen: false).loadExercises();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Exercises'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _showAddExerciseDialog,
          ),
        ],
      ),
      body: Column(
        children: [
          // Search and Filter
          Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              children: [
                // Search Bar
                TextField(
                  controller: _searchController,
                  decoration: const InputDecoration(
                    labelText: 'Search exercises',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (_) => setState(() {}),
                ),

                const SizedBox(height: AppConstants.defaultPadding),

                // Muscle Group Filter
                SizedBox(
                  height: 40,
                  child: ListView(
                    scrollDirection: Axis.horizontal,
                    children: [
                      _buildMuscleGroupChip('All'),
                      ...AppConstants.muscleGroups.map((group) => _buildMuscleGroupChip(group)),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Exercise List
          Expanded(
            child: Consumer<WorkoutProvider>(
              builder: (context, workoutProvider, child) {
                if (workoutProvider.isLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (workoutProvider.error != null) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'Error: ${workoutProvider.error}',
                          style: const TextStyle(color: AppTheme.errorColor),
                        ),
                        const SizedBox(height: AppConstants.defaultPadding),
                        ElevatedButton(
                          onPressed: () => workoutProvider.loadExercises(),
                          child: const Text('Retry'),
                        ),
                      ],
                    ),
                  );
                }

                final filteredExercises = _getFilteredExercises(workoutProvider.exercises);

                if (filteredExercises.isEmpty) {
                  return const Center(
                    child: Text('No exercises found'),
                  );
                }

                return ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
                  itemCount: filteredExercises.length,
                  itemBuilder: (context, index) {
                    final exercise = filteredExercises[index];
                    return _buildExerciseCard(exercise);
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMuscleGroupChip(String muscleGroup) {
    final isSelected = _selectedMuscleGroup == muscleGroup;

    return Padding(
      padding: const EdgeInsets.only(right: AppConstants.smallPadding),
      child: FilterChip(
        label: Text(muscleGroup),
        selected: isSelected,
        onSelected: (selected) {
          setState(() {
            _selectedMuscleGroup = muscleGroup;
          });
        },
        selectedColor: AppTheme.primaryColor.withOpacity(0.2),
        checkmarkColor: AppTheme.primaryColor,
      ),
    );
  }

  List<Exercise> _getFilteredExercises(List<Exercise> exercises) {
    return exercises.where((exercise) {
      final matchesSearch = _searchController.text.isEmpty ||
          exercise.name.toLowerCase().contains(_searchController.text.toLowerCase()) ||
          exercise.primaryMuscleGroup.toLowerCase().contains(_searchController.text.toLowerCase());

      final matchesMuscleGroup = _selectedMuscleGroup == 'All' ||
          exercise.primaryMuscleGroup == _selectedMuscleGroup ||
          exercise.secondaryMuscleGroups.contains(_selectedMuscleGroup);

      return matchesSearch && matchesMuscleGroup;
    }).toList();
  }

  Widget _buildExerciseCard(Exercise exercise) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: ExpansionTile(
        tilePadding: const EdgeInsets.all(AppConstants.defaultPadding),
        childrenPadding: const EdgeInsets.all(AppConstants.defaultPadding),
        leading: Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                _getMuscleGroupColor(exercise.primaryMuscleGroup),
                _getMuscleGroupColor(exercise.primaryMuscleGroup).withValues(alpha: 0.7),
              ],
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            _getMuscleGroupIcon(exercise.primaryMuscleGroup),
            color: Colors.white,
            size: 28,
          ),
        ),
        title: Text(
          exercise.name,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(
                  Icons.fitness_center,
                  size: 16,
                  color: Colors.grey[600],
                ),
                const SizedBox(width: 4),
                Text(
                  exercise.primaryMuscleGroup,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: _getMuscleGroupColor(exercise.primaryMuscleGroup),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(
                  Icons.build,
                  size: 16,
                  color: Colors.grey[600],
                ),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    exercise.equipmentRequired,
                    style: Theme.of(context).textTheme.bodySmall,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: _getDifficultyColor(exercise.difficultyLevel),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                exercise.difficultyLevel,
                style: const TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
            if (exercise.isCustom) ...[
              const SizedBox(height: 4),
              Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: AppTheme.secondaryColor,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.person,
                  size: 12,
                  color: Colors.white,
                ),
              ),
            ],
          ],
        ),
        children: [
          _buildExerciseDetails(exercise),
        ],
      ),
    );
  }

  Widget _buildExerciseDetails(Exercise exercise) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(top: AppConstants.smallPadding),
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getMuscleGroupColor(exercise.primaryMuscleGroup).withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Description Section
          _buildDetailSection(
            'Description',
            exercise.description,
            Icons.description,
          ),

          const SizedBox(height: AppConstants.defaultPadding),

          // Secondary Muscles
          if (exercise.secondaryMuscleGroups.isNotEmpty) ...[
            _buildDetailSection(
              'Secondary Muscles',
              exercise.secondaryMuscleGroups.join(', '),
              Icons.group_work,
            ),
            const SizedBox(height: AppConstants.defaultPadding),
          ],

          // Equipment
          _buildDetailSection(
            'Equipment Required',
            exercise.equipmentRequired,
            Icons.build,
          ),

          const SizedBox(height: AppConstants.defaultPadding),

          // Instructions
          _buildDetailSection(
            'Instructions',
            exercise.instructions,
            Icons.list_alt,
          ),

          const SizedBox(height: AppConstants.largePadding),

          // Action Buttons
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _editExercise(exercise),
                  icon: const Icon(Icons.edit, size: 18),
                  label: const Text('Edit'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _getMuscleGroupColor(exercise.primaryMuscleGroup),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: AppConstants.smallPadding),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => _addToWorkout(exercise),
                  icon: const Icon(Icons.add, size: 18),
                  label: const Text('Add to Workout'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: _getMuscleGroupColor(exercise.primaryMuscleGroup),
                    side: BorderSide(
                      color: _getMuscleGroupColor(exercise.primaryMuscleGroup),
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDetailSection(String title, String content, IconData icon) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          children: [
            Icon(icon, size: 18, color: AppTheme.primaryColor),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                title,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppTheme.primaryColor,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: Colors.grey.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          child: Text(
            content,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              height: 1.4,
            ),
            softWrap: true,
            textAlign: TextAlign.left,
          ),
        ),
      ],
    );
  }

  Color _getMuscleGroupColor(String muscleGroup) {
    switch (muscleGroup.toLowerCase()) {
      case 'quadriceps':
      case 'legs':
        return Colors.blue;
      case 'hamstrings':
        return Colors.indigo;
      case 'calves':
        return Colors.lightBlue;
      case 'glutes':
        return Colors.purple;
      case 'front deltoids':
      case 'lateral deltoids':
      case 'rear deltoids':
      case 'shoulders':
        return Colors.orange;
      case 'lats':
      case 'back':
        return Colors.green;
      case 'rhomboids':
      case 'mid-back':
        return Colors.teal;
      case 'traps':
        return Colors.cyan;
      case 'biceps long head':
      case 'biceps short head':
      case 'biceps':
        return Colors.red;
      case 'brachialis':
        return Colors.pink;
      case 'upper chest':
      case 'middle chest':
      case 'lower chest':
      case 'inner chest':
      case 'chest':
        return Colors.deepOrange;
      case 'triceps long head':
      case 'triceps lateral head':
      case 'triceps medial head':
      case 'triceps':
        return Colors.amber;
      default:
        return AppTheme.primaryColor;
    }
  }

  IconData _getMuscleGroupIcon(String muscleGroup) {
    switch (muscleGroup.toLowerCase()) {
      case 'quadriceps':
      case 'hamstrings':
      case 'calves':
      case 'glutes':
      case 'legs':
        return Icons.directions_run;
      case 'front deltoids':
      case 'lateral deltoids':
      case 'rear deltoids':
      case 'shoulders':
        return Icons.accessibility_new;
      case 'lats':
      case 'rhomboids':
      case 'mid-back':
      case 'traps':
      case 'back':
        return Icons.view_column;
      case 'biceps long head':
      case 'biceps short head':
      case 'brachialis':
      case 'biceps':
        return Icons.fitness_center;
      case 'upper chest':
      case 'middle chest':
      case 'lower chest':
      case 'inner chest':
      case 'chest':
        return Icons.favorite;
      case 'triceps long head':
      case 'triceps lateral head':
      case 'triceps medial head':
      case 'triceps':
        return Icons.sports_gymnastics;
      default:
        return Icons.fitness_center;
    }
  }

  Color _getDifficultyColor(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'beginner':
        return AppTheme.successColor;
      case 'intermediate':
        return AppTheme.warningColor;
      case 'advanced':
        return AppTheme.errorColor;
      case 'expert':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  void _editExercise(Exercise exercise) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Edit ${exercise.name}'),
        content: const Text('Exercise editing with YouTube video and URL support will be implemented in the next phase.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Navigate to exercise edit screen
            },
            child: const Text('Edit'),
          ),
        ],
      ),
    );
  }

  void _addToWorkout(Exercise exercise) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${exercise.name} added to current workout!'),
        backgroundColor: AppTheme.successColor,
        action: SnackBarAction(
          label: 'UNDO',
          onPressed: () {
            // TODO: Implement undo functionality
          },
        ),
      ),
    );
  }

  void _showAddExerciseDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Custom Exercise'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Create a new custom exercise for your workout routine.'),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              decoration: const InputDecoration(
                labelText: 'Muscle Group',
                border: OutlineInputBorder(),
              ),
              items: AppConstants.muscleGroups.map((group) {
                return DropdownMenuItem(
                  value: group,
                  child: Text(group),
                );
              }).toList(),
              onChanged: (value) {
                // TODO: Handle muscle group selection
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Navigate to custom exercise creation screen
            },
            child: const Text('Create'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
