import '../models/exercise.dart';

class ExerciseVariationsService {
  static List<Exercise> getExerciseVariations() {
    return [
      // LEGS - Quadriceps Variations
      Exercise(
        id: 'quad_var_001',
        name: 'Front Squats',
        description: 'Emphasizes overall quad development',
        primaryMuscleGroup: 'Quadriceps',
        secondaryMuscleGroups: ['Glutes', 'Core'],
        equipmentRequired: 'Barbell, Squat Rack',
        difficultyLevel: 'Advanced',
        instructions: 'Hold barbell in front rack position. Squat down keeping chest up and elbows high. Drive through heels to return.',
        defaultVariation: 'Front Squat',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'quad_var_002',
        name: 'Hack Squats',
        description: 'Targets the quads with reduced lower back stress',
        primaryMuscleGroup: 'Quadriceps',
        secondaryMuscleGroups: ['Glutes'],
        equipmentRequired: 'Hack Squat Machine',
        difficultyLevel: 'Intermediate',
        instructions: 'Position yourself in hack squat machine. Lower weight by bending knees, then drive through heels to return.',
        defaultVariation: 'Machine Hack Squat',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'quad_var_003',
        name: 'Sissy Squats',
        description: 'Emphasizes the rectus femoris (front quad)',
        primaryMuscleGroup: 'Quadriceps',
        secondaryMuscleGroups: [],
        equipmentRequired: 'Sissy Squat Machine or Bodyweight',
        difficultyLevel: 'Advanced',
        instructions: 'Lean back while bending knees, keeping torso straight. Lower until deep stretch, then return to start.',
        defaultVariation: 'Bodyweight Sissy Squat',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'quad_var_004',
        name: 'Step-Ups',
        description: 'Functional quad exercise with balance component',
        primaryMuscleGroup: 'Quadriceps',
        secondaryMuscleGroups: ['Glutes', 'Calves'],
        equipmentRequired: 'Box or Bench, Dumbbells (optional)',
        difficultyLevel: 'Beginner',
        instructions: 'Step up onto box with one foot, drive through heel to stand. Step down with control and repeat.',
        defaultVariation: 'Bodyweight Step-Up',
        createdAt: DateTime.now(),
      ),

      // LEGS - Hamstrings Variations
      Exercise(
        id: 'ham_var_001',
        name: 'Seated Leg Curls',
        description: 'Alternative hamstring isolation',
        primaryMuscleGroup: 'Hamstrings',
        secondaryMuscleGroups: [],
        equipmentRequired: 'Seated Leg Curl Machine',
        difficultyLevel: 'Beginner',
        instructions: 'Sit in machine with back against pad. Curl heels toward glutes, squeeze at top, then lower slowly.',
        defaultVariation: 'Seated Leg Curl',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'ham_var_002',
        name: 'Good Mornings',
        description: 'Targets hamstrings and lower back',
        primaryMuscleGroup: 'Hamstrings',
        secondaryMuscleGroups: ['Lower Back', 'Glutes'],
        equipmentRequired: 'Barbell',
        difficultyLevel: 'Intermediate',
        instructions: 'Place barbell on upper back. Hinge at hips, lower torso while keeping back straight. Return to start.',
        defaultVariation: 'Barbell Good Morning',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'ham_var_003',
        name: 'Nordic Hamstring Curls',
        description: 'Eccentric hamstring strengthening',
        primaryMuscleGroup: 'Hamstrings',
        secondaryMuscleGroups: [],
        equipmentRequired: 'Partner or Anchor Point',
        difficultyLevel: 'Advanced',
        instructions: 'Kneel with feet anchored. Lower body forward slowly using hamstrings to control descent.',
        defaultVariation: 'Nordic Hamstring Curl',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'ham_var_004',
        name: 'Stiff-Legged Deadlifts',
        description: 'Emphasizes hamstring and glute stretch',
        primaryMuscleGroup: 'Hamstrings',
        secondaryMuscleGroups: ['Glutes', 'Lower Back'],
        equipmentRequired: 'Barbell or Dumbbells',
        difficultyLevel: 'Intermediate',
        instructions: 'Keep legs straight, hinge at hips to lower weight. Feel stretch in hamstrings, then return to start.',
        defaultVariation: 'Barbell Stiff-Legged Deadlift',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'ham_var_005',
        name: 'Single-Leg Deadlifts',
        description: 'Unilateral posterior chain development',
        primaryMuscleGroup: 'Hamstrings',
        secondaryMuscleGroups: ['Glutes', 'Core'],
        equipmentRequired: 'Dumbbells',
        difficultyLevel: 'Intermediate',
        instructions: 'Stand on one leg, hinge at hip while extending other leg back. Lower weight toward ground, then return.',
        defaultVariation: 'Single-Leg Romanian Deadlift',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'ham_var_006',
        name: 'Cable Pull-Throughs',
        description: 'Targets hamstrings with constant tension',
        primaryMuscleGroup: 'Hamstrings',
        secondaryMuscleGroups: ['Glutes'],
        equipmentRequired: 'Cable Machine, Rope Attachment',
        difficultyLevel: 'Beginner',
        instructions: 'Face away from cable, pull rope through legs by driving hips forward. Squeeze glutes at top.',
        defaultVariation: 'Cable Pull-Through',
        createdAt: DateTime.now(),
      ),

      // LEGS - Calves Variations
      Exercise(
        id: 'calf_var_001',
        name: 'Donkey Calf Raises',
        description: 'Allows greater stretch and contraction',
        primaryMuscleGroup: 'Calves',
        secondaryMuscleGroups: [],
        equipmentRequired: 'Donkey Calf Raise Machine',
        difficultyLevel: 'Intermediate',
        instructions: 'Bend forward at hips, rise up on toes as high as possible. Lower to deep stretch position.',
        defaultVariation: 'Machine Donkey Calf Raise',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'calf_var_002',
        name: 'Single-Leg Calf Raises',
        description: 'Unilateral calf development',
        primaryMuscleGroup: 'Calves',
        secondaryMuscleGroups: [],
        equipmentRequired: 'Dumbbell (optional)',
        difficultyLevel: 'Beginner',
        instructions: 'Stand on one foot, rise up on toes. Hold peak contraction, then lower slowly.',
        defaultVariation: 'Single-Leg Calf Raise',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'calf_var_003',
        name: 'Calf Press on Leg Press',
        description: 'Allows heavy loading with support',
        primaryMuscleGroup: 'Calves',
        secondaryMuscleGroups: [],
        equipmentRequired: 'Leg Press Machine',
        difficultyLevel: 'Beginner',
        instructions: 'Sit in leg press, place balls of feet on platform. Press through toes, hold peak, then lower.',
        defaultVariation: 'Leg Press Calf Press',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'calf_var_004',
        name: 'Jump Rope',
        description: 'Dynamic calf exercise with cardiovascular benefit',
        primaryMuscleGroup: 'Calves',
        secondaryMuscleGroups: ['Cardio'],
        equipmentRequired: 'Jump Rope',
        difficultyLevel: 'Beginner',
        instructions: 'Jump rope continuously, staying on balls of feet. Land softly and maintain rhythm.',
        defaultVariation: 'Basic Jump Rope',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'calf_var_005',
        name: 'Box Jumps',
        description: 'Explosive calf development',
        primaryMuscleGroup: 'Calves',
        secondaryMuscleGroups: ['Quadriceps', 'Glutes'],
        equipmentRequired: 'Plyometric Box',
        difficultyLevel: 'Intermediate',
        instructions: 'Jump explosively onto box, land softly. Step down and repeat.',
        defaultVariation: 'Box Jump',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'calf_var_006',
        name: 'Tibialis Raises',
        description: 'Targets the often-neglected anterior tibialis',
        primaryMuscleGroup: 'Calves',
        secondaryMuscleGroups: [],
        equipmentRequired: 'None or Resistance Band',
        difficultyLevel: 'Beginner',
        instructions: 'Sit with heels on ground, lift toes up as high as possible. Hold and lower slowly.',
        defaultVariation: 'Seated Tibialis Raise',
        createdAt: DateTime.now(),
      ),

      // LEGS - Glutes Variations
      Exercise(
        id: 'glute_var_001',
        name: 'Glute Bridges',
        description: 'Beginner-friendly glute activation',
        primaryMuscleGroup: 'Glutes',
        secondaryMuscleGroups: ['Hamstrings'],
        equipmentRequired: 'None',
        difficultyLevel: 'Beginner',
        instructions: 'Lie on back, knees bent. Drive hips up by squeezing glutes. Hold at top, then lower.',
        defaultVariation: 'Bodyweight Glute Bridge',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'glute_var_002',
        name: 'Sumo Squats',
        description: 'Wide stance targets inner thighs and glutes',
        primaryMuscleGroup: 'Glutes',
        secondaryMuscleGroups: ['Quadriceps', 'Adductors'],
        equipmentRequired: 'Dumbbell or Kettlebell',
        difficultyLevel: 'Beginner',
        instructions: 'Stand with wide stance, toes pointed out. Squat down keeping chest up, drive through heels.',
        defaultVariation: 'Sumo Squat',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'glute_var_003',
        name: 'Reverse Lunges',
        description: 'Emphasizes glute engagement',
        primaryMuscleGroup: 'Glutes',
        secondaryMuscleGroups: ['Quadriceps', 'Hamstrings'],
        equipmentRequired: 'Dumbbells (optional)',
        difficultyLevel: 'Beginner',
        instructions: 'Step backward into lunge position. Drive through front heel to return to start.',
        defaultVariation: 'Bodyweight Reverse Lunge',
        createdAt: DateTime.now(),
      ),
    ];
  }
}
