import '../models/exercise.dart';

class ExerciseVariationsPart2 {
  static List<Exercise> getMoreExerciseVariations() {
    return [
      // GLUTES - More Variations
      Exercise(
        id: 'glute_var_004',
        name: 'Curtsy Lunges',
        description: 'Targets gluteus medius (side glute)',
        primaryMuscleGroup: 'Glutes',
        secondaryMuscleGroups: ['Quadriceps'],
        equipmentRequired: 'Dumbbells (optional)',
        difficultyLevel: 'Intermediate',
        instructions: 'Step one leg behind and across the other into curtsy position. Return to start and repeat.',
        defaultVariation: 'Bodyweight Curtsy Lunge',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'glute_var_005',
        name: 'Cable Kickbacks',
        description: 'Isolated glute contraction with constant tension',
        primaryMuscleGroup: 'Glutes',
        secondaryMuscleGroups: [],
        equipmentRequired: 'Cable Machine, Ankle Strap',
        difficultyLevel: 'Beginner',
        instructions: 'Attach ankle strap to cable. Kick leg back while keeping core tight. Squeeze glute at top.',
        defaultVariation: 'Cable Glute Kickback',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'glute_var_006',
        name: '<PERSON> Pumps',
        description: 'Targets glute medius and minimus',
        primaryMuscleGroup: 'Glutes',
        secondaryMuscleGroups: [],
        equipmentRequired: 'None',
        difficultyLevel: 'Beginner',
        instructions: 'Lie on back with soles of feet together. Drive hips up by squeezing glutes.',
        defaultVariation: 'Frog Pump',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'glute_var_007',
        name: 'Fire Hydrants',
        description: 'Activates gluteus medius',
        primaryMuscleGroup: 'Glutes',
        secondaryMuscleGroups: ['Core'],
        equipmentRequired: 'None',
        difficultyLevel: 'Beginner',
        instructions: 'Start on hands and knees. Lift one leg out to side, keeping knee bent. Lower and repeat.',
        defaultVariation: 'Fire Hydrant',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'glute_var_008',
        name: 'Lateral Band Walks',
        description: 'Strengthens hip abductors and glutes',
        primaryMuscleGroup: 'Glutes',
        secondaryMuscleGroups: ['Hip Abductors'],
        equipmentRequired: 'Resistance Band',
        difficultyLevel: 'Beginner',
        instructions: 'Place band around legs. Step sideways maintaining tension. Keep knees slightly bent.',
        defaultVariation: 'Lateral Band Walk',
        createdAt: DateTime.now(),
      ),

      // SHOULDERS - Front Deltoids Variations
      Exercise(
        id: 'front_delt_var_001',
        name: 'Machine Shoulder Press',
        description: 'Primary front delt builder with stability',
        primaryMuscleGroup: 'Front Deltoids',
        secondaryMuscleGroups: ['Lateral Deltoids', 'Triceps'],
        equipmentRequired: 'Shoulder Press Machine',
        difficultyLevel: 'Beginner',
        instructions: 'Sit in machine with back supported. Press handles overhead until arms are extended.',
        defaultVariation: 'Machine Shoulder Press',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'front_delt_var_002',
        name: 'EZ-Bar Front Raises',
        description: 'Underhand grip for better range of motion',
        primaryMuscleGroup: 'Front Deltoids',
        secondaryMuscleGroups: [],
        equipmentRequired: 'EZ-Bar',
        difficultyLevel: 'Intermediate',
        instructions: 'Hold EZ-bar with underhand grip. Raise to shoulder height with slight bend in elbows.',
        defaultVariation: 'EZ-Bar Front Raise',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'front_delt_var_003',
        name: 'Barbell Military Press',
        description: 'Heavy compound for overall shoulder development',
        primaryMuscleGroup: 'Front Deltoids',
        secondaryMuscleGroups: ['Lateral Deltoids', 'Triceps', 'Core'],
        equipmentRequired: 'Barbell',
        difficultyLevel: 'Advanced',
        instructions: 'Stand with barbell at shoulder level. Press overhead while keeping core tight.',
        defaultVariation: 'Standing Military Press',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'front_delt_var_004',
        name: 'Arnold Press',
        description: 'Rotational movement targeting front delts',
        primaryMuscleGroup: 'Front Deltoids',
        secondaryMuscleGroups: ['Lateral Deltoids'],
        equipmentRequired: 'Dumbbells',
        difficultyLevel: 'Intermediate',
        instructions: 'Start with palms facing you. Press up while rotating palms forward at top.',
        defaultVariation: 'Seated Arnold Press',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'front_delt_var_005',
        name: 'Cable Front Raises',
        description: 'Constant tension throughout the movement',
        primaryMuscleGroup: 'Front Deltoids',
        secondaryMuscleGroups: [],
        equipmentRequired: 'Cable Machine',
        difficultyLevel: 'Beginner',
        instructions: 'Stand facing away from cable. Raise handle forward to shoulder height.',
        defaultVariation: 'Cable Front Raise',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'front_delt_var_006',
        name: 'Plate Front Raises',
        description: 'Simple but effective front delt isolation',
        primaryMuscleGroup: 'Front Deltoids',
        secondaryMuscleGroups: [],
        equipmentRequired: 'Weight Plate',
        difficultyLevel: 'Beginner',
        instructions: 'Hold plate with both hands. Raise to shoulder height with arms extended.',
        defaultVariation: 'Plate Front Raise',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'front_delt_var_007',
        name: 'Push Press',
        description: 'Explosive front delt development with some leg drive',
        primaryMuscleGroup: 'Front Deltoids',
        secondaryMuscleGroups: ['Lateral Deltoids', 'Triceps', 'Legs'],
        equipmentRequired: 'Barbell',
        difficultyLevel: 'Advanced',
        instructions: 'Use slight leg drive to help press weight overhead explosively.',
        defaultVariation: 'Barbell Push Press',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'front_delt_var_008',
        name: 'Incline Bench Front Raises',
        description: 'Supported variation for strict form',
        primaryMuscleGroup: 'Front Deltoids',
        secondaryMuscleGroups: [],
        equipmentRequired: 'Dumbbells, Incline Bench',
        difficultyLevel: 'Intermediate',
        instructions: 'Lie on incline bench. Raise dumbbells forward to shoulder height.',
        defaultVariation: 'Incline Front Raise',
        createdAt: DateTime.now(),
      ),

      // SHOULDERS - Lateral Deltoids Variations
      Exercise(
        id: 'lat_delt_var_001',
        name: 'Leaning Lateral Raises',
        description: 'Enhanced range of motion with body positioning',
        primaryMuscleGroup: 'Lateral Deltoids',
        secondaryMuscleGroups: [],
        equipmentRequired: 'Dumbbells',
        difficultyLevel: 'Intermediate',
        instructions: 'Lean to one side while holding support. Raise opposite arm out to side.',
        defaultVariation: 'Leaning Lateral Raise',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'lat_delt_var_002',
        name: 'Cable Lateral Raises',
        description: 'Constant tension throughout the movement',
        primaryMuscleGroup: 'Lateral Deltoids',
        secondaryMuscleGroups: [],
        equipmentRequired: 'Cable Machine',
        difficultyLevel: 'Beginner',
        instructions: 'Stand beside cable machine. Raise handle out to side to shoulder height.',
        defaultVariation: 'Cable Lateral Raise',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'lat_delt_var_003',
        name: 'Machine Lateral Raises',
        description: 'Guided movement for strict form',
        primaryMuscleGroup: 'Lateral Deltoids',
        secondaryMuscleGroups: [],
        equipmentRequired: 'Lateral Raise Machine',
        difficultyLevel: 'Beginner',
        instructions: 'Sit in machine with arms against pads. Raise arms out to sides.',
        defaultVariation: 'Machine Lateral Raise',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'lat_delt_var_004',
        name: 'High Pulls',
        description: 'Explosive movement for side delt power',
        primaryMuscleGroup: 'Lateral Deltoids',
        secondaryMuscleGroups: ['Traps', 'Rear Deltoids'],
        equipmentRequired: 'Barbell',
        difficultyLevel: 'Advanced',
        instructions: 'Pull barbell explosively to chest level, leading with elbows.',
        defaultVariation: 'Barbell High Pull',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'lat_delt_var_005',
        name: 'Kettlebell Lateral Raises',
        description: 'Alternative loading for side delts',
        primaryMuscleGroup: 'Lateral Deltoids',
        secondaryMuscleGroups: [],
        equipmentRequired: 'Kettlebells',
        difficultyLevel: 'Intermediate',
        instructions: 'Hold kettlebells at sides. Raise out to shoulder height.',
        defaultVariation: 'Kettlebell Lateral Raise',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'lat_delt_var_006',
        name: 'Banded Lateral Raises',
        description: 'Accommodating resistance for peak contraction',
        primaryMuscleGroup: 'Lateral Deltoids',
        secondaryMuscleGroups: [],
        equipmentRequired: 'Resistance Bands',
        difficultyLevel: 'Beginner',
        instructions: 'Stand on band, raise handles out to sides. Resistance increases at top.',
        defaultVariation: 'Band Lateral Raise',
        createdAt: DateTime.now(),
      ),

      // SHOULDERS - Rear Deltoids Variations
      Exercise(
        id: 'rear_delt_var_001',
        name: 'Reverse Cable Flyes',
        description: 'Optimal alignment for rear delt targeting',
        primaryMuscleGroup: 'Rear Deltoids',
        secondaryMuscleGroups: ['Rhomboids'],
        equipmentRequired: 'Cable Machine',
        difficultyLevel: 'Beginner',
        instructions: 'Set cables at shoulder height. Pull handles back with arms extended.',
        defaultVariation: 'Cable Reverse Fly',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'rear_delt_var_002',
        name: 'Prone Rear Delt Raises',
        description: 'Lying variation for strict form',
        primaryMuscleGroup: 'Rear Deltoids',
        secondaryMuscleGroups: [],
        equipmentRequired: 'Dumbbells, Bench',
        difficultyLevel: 'Intermediate',
        instructions: 'Lie face down on bench. Raise dumbbells out to sides with slight bend in elbows.',
        defaultVariation: 'Prone Rear Delt Raise',
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 'rear_delt_var_003',
        name: 'Seated Bent-Over Raises',
        description: 'Supported variation for heavier loads',
        primaryMuscleGroup: 'Rear Deltoids',
        secondaryMuscleGroups: [],
        equipmentRequired: 'Dumbbells, Bench',
        difficultyLevel: 'Intermediate',
        instructions: 'Sit on bench, bend forward. Raise dumbbells out to sides.',
        defaultVariation: 'Seated Bent-Over Raise',
        createdAt: DateTime.now(),
      ),
    ];
  }
}
