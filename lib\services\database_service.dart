import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path_provider/path_provider.dart';
import '../models/user_profile.dart';
import '../models/exercise.dart';
import '../models/workout.dart';
import '../models/workout_exercise.dart';
import '../models/personal_metric.dart';
import 'exercise_data_service.dart';
import 'exercise_data_service_part2.dart';
import 'exercise_variations_service.dart';
import 'exercise_variations_part2.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    Directory documentsDirectory = await getApplicationDocumentsDirectory();
    String path = join(documentsDirectory.path, 'workout_tracker.db');

    return await openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // Create user_profiles table
    await db.execute('''
      CREATE TABLE user_profiles (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        email TEXT NOT NULL UNIQUE,
        profile_picture TEXT,
        height REAL,
        target_weight REAL,
        fitness_goals TEXT,
        privacy_settings TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // Create exercises table
    await db.execute('''
      CREATE TABLE exercises (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT NOT NULL,
        primary_muscle_group TEXT NOT NULL,
        secondary_muscle_groups TEXT,
        equipment_required TEXT NOT NULL,
        difficulty_level TEXT NOT NULL,
        instructions TEXT NOT NULL,
        default_variation TEXT NOT NULL,
        is_custom INTEGER DEFAULT 0,
        created_at TEXT NOT NULL
      )
    ''');

    // Create workouts table
    await db.execute('''
      CREATE TABLE workouts (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        date TEXT NOT NULL,
        start_time TEXT,
        end_time TEXT,
        pre_workout_weight REAL,
        post_workout_weight REAL,
        cardio_performed INTEGER DEFAULT 0,
        cardio_details TEXT,
        notes TEXT,
        overall_rating INTEGER,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (user_id) REFERENCES user_profiles (id)
      )
    ''');

    // Create workout_exercises table
    await db.execute('''
      CREATE TABLE workout_exercises (
        id TEXT PRIMARY KEY,
        workout_id TEXT NOT NULL,
        exercise_id TEXT NOT NULL,
        variation_used TEXT NOT NULL,
        sets_data TEXT,
        notes TEXT,
        rating INTEGER,
        order_index INTEGER NOT NULL,
        created_at TEXT NOT NULL,
        FOREIGN KEY (workout_id) REFERENCES workouts (id),
        FOREIGN KEY (exercise_id) REFERENCES exercises (id)
      )
    ''');

    // Create personal_metrics table
    await db.execute('''
      CREATE TABLE personal_metrics (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        date TEXT NOT NULL,
        metric_type TEXT NOT NULL,
        metric_value TEXT NOT NULL,
        notes TEXT,
        privacy_level INTEGER NOT NULL,
        created_at TEXT NOT NULL,
        FOREIGN KEY (user_id) REFERENCES user_profiles (id)
      )
    ''');

    // Create custom_metrics table
    await db.execute('''
      CREATE TABLE custom_metrics (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        metric_name TEXT NOT NULL,
        metric_type INTEGER NOT NULL,
        default_privacy_level INTEGER NOT NULL,
        display_on_dashboard INTEGER DEFAULT 1,
        created_at TEXT NOT NULL,
        FOREIGN KEY (user_id) REFERENCES user_profiles (id)
      )
    ''');

    // Create indexes for better performance
    await db.execute('CREATE INDEX idx_workouts_date ON workouts(date)');
    await db.execute('CREATE INDEX idx_workouts_user_id ON workouts(user_id)');
    await db.execute('CREATE INDEX idx_personal_metrics_date ON personal_metrics(date)');
    await db.execute('CREATE INDEX idx_personal_metrics_user_id ON personal_metrics(user_id)');

    // Insert default exercises
    await _insertDefaultExercises(db);
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle database upgrades here
  }

  Future<void> _insertDefaultExercises(Database db) async {
    // Get all exercises from the exercise data services
    final allExercises = [
      ...ExerciseDataService.getAllExercises(),
      ...ExerciseDataServicePart2.getBicepsChestTricepsExercises(),
      ...ExerciseVariationsService.getExerciseVariations(),
      ...ExerciseVariationsPart2.getMoreExerciseVariations(),
    ];

    // Insert each exercise into the database
    for (var exercise in allExercises) {
      try {
        await db.insert('exercises', exercise.toMap());
      } catch (e) {
        // Skip if exercise already exists (duplicate key)
        // Using debugPrint instead of print for production
        debugPrint('Exercise ${exercise.name} already exists or error: $e');
      }
    }
  }

  // User Profile methods
  Future<int> insertUserProfile(UserProfile profile) async {
    final db = await database;
    return await db.insert('user_profiles', profile.toMap());
  }

  Future<UserProfile?> getUserProfile(String id) async {
    final db = await database;
    final maps = await db.query(
      'user_profiles',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return UserProfile.fromMap(maps.first);
    }
    return null;
  }

  Future<int> updateUserProfile(UserProfile profile) async {
    final db = await database;
    return await db.update(
      'user_profiles',
      profile.toMap(),
      where: 'id = ?',
      whereArgs: [profile.id],
    );
  }

  // Exercise methods
  Future<List<Exercise>> getAllExercises() async {
    final db = await database;
    final maps = await db.query('exercises', orderBy: 'name ASC');
    return maps.map((map) => Exercise.fromMap(map)).toList();
  }

  Future<List<Exercise>> getExercisesByMuscleGroup(String muscleGroup) async {
    final db = await database;
    final maps = await db.query(
      'exercises',
      where: 'primary_muscle_group = ? OR secondary_muscle_groups LIKE ?',
      whereArgs: [muscleGroup, '%$muscleGroup%'],
      orderBy: 'name ASC',
    );
    return maps.map((map) => Exercise.fromMap(map)).toList();
  }

  Future<int> insertExercise(Exercise exercise) async {
    final db = await database;
    return await db.insert('exercises', exercise.toMap());
  }

  // Workout methods
  Future<int> insertWorkout(Workout workout) async {
    final db = await database;
    return await db.insert('workouts', workout.toMap());
  }

  Future<List<Workout>> getWorkoutsByDateRange(DateTime start, DateTime end) async {
    final db = await database;
    final maps = await db.query(
      'workouts',
      where: 'date BETWEEN ? AND ?',
      whereArgs: [start.toIso8601String(), end.toIso8601String()],
      orderBy: 'date DESC',
    );
    return maps.map((map) => Workout.fromMap(map)).toList();
  }

  Future<List<Workout>> getWorkoutsByDate(DateTime date) async {
    final db = await database;
    final dateStr = date.toIso8601String().split('T')[0]; // Get date part only
    final maps = await db.query(
      'workouts',
      where: 'date LIKE ?',
      whereArgs: ['$dateStr%'],
      orderBy: 'start_time ASC',
    );
    return maps.map((map) => Workout.fromMap(map)).toList();
  }

  Future<int> updateWorkout(Workout workout) async {
    final db = await database;
    return await db.update(
      'workouts',
      workout.toMap(),
      where: 'id = ?',
      whereArgs: [workout.id],
    );
  }

  Future<int> deleteWorkout(String workoutId) async {
    final db = await database;
    return await db.delete(
      'workouts',
      where: 'id = ?',
      whereArgs: [workoutId],
    );
  }

  // Personal Metrics methods
  Future<int> insertPersonalMetric(PersonalMetric metric) async {
    final db = await database;
    return await db.insert('personal_metrics', metric.toMap());
  }

  Future<List<PersonalMetric>> getPersonalMetricsByDateRange(
    DateTime start,
    DateTime end,
    {String? metricType}
  ) async {
    final db = await database;
    String whereClause = 'date BETWEEN ? AND ?';
    List<dynamic> whereArgs = [start.toIso8601String(), end.toIso8601String()];

    if (metricType != null) {
      whereClause += ' AND metric_type = ?';
      whereArgs.add(metricType);
    }

    final maps = await db.query(
      'personal_metrics',
      where: whereClause,
      whereArgs: whereArgs,
      orderBy: 'date DESC',
    );
    return maps.map((map) => PersonalMetric.fromMap(map)).toList();
  }

  // Close database
  Future<void> close() async {
    final db = await database;
    await db.close();
  }
}
