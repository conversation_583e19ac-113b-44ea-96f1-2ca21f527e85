import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:intl/intl.dart';
import '../providers/metrics_provider.dart';
import '../models/personal_metric.dart';
import '../utils/app_theme.dart';
import '../utils/constants.dart';

class MetricsScreen extends StatefulWidget {
  const MetricsScreen({super.key});

  @override
  State<MetricsScreen> createState() => _MetricsScreenState();
}

class _MetricsScreenState extends State<MetricsScreen> {
  String _selectedMetric = 'Weight';
  int _selectedDays = 30;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadMetrics();
    });
  }

  void _loadMetrics() {
    final metricsProvider = Provider.of<MetricsProvider>(context, listen: false);
    final endDate = DateTime.now();
    final startDate = endDate.subtract(Duration(days: _selectedDays));
    metricsProvider.loadMetrics(startDate, endDate);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Metrics'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _showAddMetricDialog,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportData,
          ),
        ],
      ),
      body: Column(
        children: [
          // Metric Selector and Time Range
          Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _selectedMetric,
                    decoration: const InputDecoration(
                      labelText: 'Metric',
                      border: OutlineInputBorder(),
                    ),
                    items: AppConstants.defaultPersonalMetrics.map((metric) {
                      return DropdownMenuItem(
                        value: metric,
                        child: Text(metric),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _selectedMetric = value;
                        });
                      }
                    },
                  ),
                ),
                const SizedBox(width: AppConstants.defaultPadding),
                Expanded(
                  child: DropdownButtonFormField<int>(
                    value: _selectedDays,
                    decoration: const InputDecoration(
                      labelText: 'Time Range',
                      border: OutlineInputBorder(),
                    ),
                    items: const [
                      DropdownMenuItem(value: 7, child: Text('7 days')),
                      DropdownMenuItem(value: 30, child: Text('30 days')),
                      DropdownMenuItem(value: 90, child: Text('90 days')),
                      DropdownMenuItem(value: 365, child: Text('1 year')),
                    ],
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _selectedDays = value;
                        });
                        _loadMetrics();
                      }
                    },
                  ),
                ),
              ],
            ),
          ),

          // Metrics Dashboard
          Expanded(
            child: Consumer<MetricsProvider>(
              builder: (context, metricsProvider, child) {
                if (metricsProvider.isLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (metricsProvider.error != null) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'Error: ${metricsProvider.error}',
                          style: const TextStyle(color: AppTheme.errorColor),
                        ),
                        const SizedBox(height: AppConstants.defaultPadding),
                        ElevatedButton(
                          onPressed: _loadMetrics,
                          child: const Text('Retry'),
                        ),
                      ],
                    ),
                  );
                }

                return SingleChildScrollView(
                  padding: const EdgeInsets.all(AppConstants.defaultPadding),
                  child: Column(
                    children: [
                      // Summary Cards
                      _buildSummaryCards(metricsProvider),

                      const SizedBox(height: AppConstants.defaultPadding),

                      // Chart
                      _buildChart(metricsProvider),

                      const SizedBox(height: AppConstants.defaultPadding),

                      // Recent Entries
                      _buildRecentEntries(metricsProvider),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCards(MetricsProvider metricsProvider) {
    final latest = metricsProvider.getLatestMetric(_selectedMetric);
    final average = metricsProvider.getAverageMetricValue(_selectedMetric, _selectedDays);
    final trend = metricsProvider.getMetricTrend(_selectedMetric, _selectedDays);

    return Row(
      children: [
        Expanded(
          child: Card(
            child: Padding(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Column(
                children: [
                  Text(
                    'Latest',
                    style: Theme.of(context).textTheme.titleSmall,
                  ),
                  Text(
                    latest?.metricValue?.toString() ?? 'N/A',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                ],
              ),
            ),
          ),
        ),
        Expanded(
          child: Card(
            child: Padding(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Column(
                children: [
                  Text(
                    'Average',
                    style: Theme.of(context).textTheme.titleSmall,
                  ),
                  Text(
                    average?.toStringAsFixed(1) ?? 'N/A',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                ],
              ),
            ),
          ),
        ),
        Expanded(
          child: Card(
            child: Padding(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Column(
                children: [
                  Text(
                    'Trend',
                    style: Theme.of(context).textTheme.titleSmall,
                  ),
                  Icon(
                    trend > 0 ? Icons.trending_up :
                    trend < 0 ? Icons.trending_down : Icons.trending_flat,
                    color: trend > 0 ? AppTheme.successColor :
                           trend < 0 ? AppTheme.errorColor : Colors.grey,
                    size: 32,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildChart(MetricsProvider metricsProvider) {
    final metrics = metricsProvider.getMetricsByType(_selectedMetric);

    if (metrics.isEmpty) {
      return Card(
        child: Container(
          height: 200,
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: const Center(
            child: Text('No data available for chart'),
          ),
        ),
      );
    }

    // Filter metrics for the selected time range
    final endDate = DateTime.now();
    final startDate = endDate.subtract(Duration(days: _selectedDays));
    final filteredMetrics = metrics.where((metric) =>
      metric.date.isAfter(startDate) && metric.metricValue is num
    ).toList();

    if (filteredMetrics.isEmpty) {
      return Card(
        child: Container(
          height: 200,
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: const Center(
            child: Text('No data in selected time range'),
          ),
        ),
      );
    }

    // Sort by date
    filteredMetrics.sort((a, b) => a.date.compareTo(b.date));

    final spots = filteredMetrics.asMap().entries.map((entry) {
      final index = entry.key;
      final metric = entry.value;
      return FlSpot(index.toDouble(), (metric.metricValue as num).toDouble());
    }).toList();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '$_selectedMetric Trend',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            SizedBox(
              height: 200,
              child: LineChart(
                LineChartData(
                  gridData: const FlGridData(show: true),
                  titlesData: FlTitlesData(
                    leftTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: true),
                    ),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          if (value.toInt() >= 0 && value.toInt() < filteredMetrics.length) {
                            final metric = filteredMetrics[value.toInt()];
                            return Text(
                              DateFormat('MM/dd').format(metric.date),
                              style: const TextStyle(fontSize: 10),
                            );
                          }
                          return const Text('');
                        },
                      ),
                    ),
                    topTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    rightTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                  ),
                  borderData: FlBorderData(show: true),
                  lineBarsData: [
                    LineChartBarData(
                      spots: spots,
                      isCurved: true,
                      color: AppTheme.primaryColor,
                      barWidth: 3,
                      dotData: const FlDotData(show: true),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentEntries(MetricsProvider metricsProvider) {
    final recentMetrics = metricsProvider.getMetricsByType(_selectedMetric)
        .take(10)
        .toList();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Recent Entries',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            if (recentMetrics.isEmpty)
              const Text('No entries found')
            else
              ...recentMetrics.map((metric) => ListTile(
                title: Text(metric.metricValue.toString()),
                subtitle: Text(DateFormat('MMM dd, yyyy').format(metric.date)),
                trailing: metric.notes != null && metric.notes!.isNotEmpty
                    ? const Icon(Icons.note)
                    : null,
              )),
          ],
        ),
      ),
    );
  }

  void _showAddMetricDialog() {
    // TODO: Implement add metric dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Metric'),
        content: const Text('Metric entry form will be implemented in the next phase.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _exportData() {
    // TODO: Implement data export
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Data export will be implemented in the next phase.'),
      ),
    );
  }
}
